import path from "path";
import { defineConfig } from "vite";
import tailwindcss from "@tailwindcss/vite";
import { redwood } from "rwsdk/vite";

export default defineConfig({
  environments: {
    ssr: {},
  },
  plugins: [redwood(), tailwindcss()],
  resolve: {
    alias: {
      "@prisma/client": path.resolve(
        __dirname,
        "node_modules",
        "@prisma",
        "client"
      ),
    },
  },
});
