import { route } from "rwsdk/router";
import { Login } from "./Login";
import { Signup } from "./Signup";
import ForgotPassword from "./ForgotPassword";
import ResetPassword from "./ResetPassword";
import { sessions } from "@/session/store";
import AuthSettings from "./settings/AuthSettings";
import ProfileSetup from "./profile/ProfileSetup";
import ProfileView from "./profile/ProfileView";
import ProfilePage from "./profile/ProfilePage";
import ProfileEditPage from "./profile/ProfileEditPage";
import { AppContext } from "@/worker";
import { db } from "@/db";
import { updateUserProfile } from "./profile/functions";
import { requestInfo } from "rwsdk/worker";

// Middleware to require authentication
const isAuthenticated = ({ ctx }: { ctx: AppContext }) => {
  if (!ctx.user) {
    return new Response(null, {
      status: 302,
      headers: { Location: "/user/login" },
    });
  }
};

export const userRoutes = [
  route("/login", [Login]),
  route("/signup", [Signup]),
  route("/forgot-password", ForgotPassword),
  route("/reset-password", ResetPassword),

  route("/:id/settings", [isAuthenticated, ({ ctx }) => {
    // Check if user exists in ctx (guaranteed by isAuthenticated middleware)
    if (!ctx.user) {
      // This case should not be reached due to isAuthenticated, but good practice
       return new Response("Unauthorized", { status: 401 });
    }

    // Serialize user data to plain object for client component
    const serializedUser = {
      id: ctx.user.id,
      username: ctx.user.username,
      email: ctx.user.email,
      role: ctx.user.role,
      createdAt: ctx.user.createdAt?.toISOString(),
      club: ctx.user.club,
    };

    return <AuthSettings user={serializedUser} />;
  }]),

  // Profile routes
  route("/:id/profile/setup", [isAuthenticated, (props) => {
    if (!props.ctx.user) {
      return new Response("Unauthorized", { status: 401 });
    }

    // Serialize user data to plain object for client component
    const serializedUser = {
      id: props.ctx.user.id,
      username: props.ctx.user.username,
      email: props.ctx.user.email,
      role: props.ctx.user.role,
      createdAt: props.ctx.user.createdAt?.toISOString(),
      club: props.ctx.user.club,
    };

    return <ProfileSetup user={serializedUser} />;
  }]),

  route("/:username/profile/edit", async (props) => {
    const username = props.params.username;
    const isOwnProfile = props.ctx.user?.username === username;

    // Find the user by username to get their ID
    const targetUser = await db.user.findUnique({
      where: { username },
      select: { id: true }
    });

    if (!targetUser) {
      return new Response("User not found", { status: 404 });
    }

    return <ProfileEditPage UserId={targetUser.id} isOwnProfile={isOwnProfile} {...props} />;
  }),

  route("/:username/profile", async (props) => {
    const username = props.params.username;
    const isOwnProfile = props.ctx.user?.username === username;

    // Find the user by username to get their ID
    const targetUser = await db.user.findUnique({
      where: { username },
      select: { id: true }
    });

    if (!targetUser) {
      return new Response("User not found", { status: 404 });
    }

    return <ProfilePage UserId={targetUser.id} isOwnProfile={isOwnProfile} {...props} />;
  }),

  // API route for updating profile
  route("/api/profile/update", async ({ request }) => {
    try {
      const { ctx } = requestInfo;

      // Check if user is authenticated
      if (!ctx.user) {
        return Response.json({ error: "Unauthorized" }, { status: 401 });
      }

      const body = await request.json() as { userId: string; profileData: any };
      const { userId, profileData } = body;

      // Verify the user is updating their own profile
      if (ctx.user.id !== userId) {
        return Response.json({ error: "Forbidden" }, { status: 403 });
      }

      // Update the profile
      const result = await updateUserProfile(userId, profileData);

      if (result) {
        return Response.json({ success: true, profile: result });
      } else {
        return Response.json({ error: "Failed to update profile" }, { status: 500 });
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      return Response.json({ error: "Internal server error" }, { status: 500 });
    }
  }),

  route("/logout", async function ({ request }) {
    const headers = new Headers();
    await sessions.remove(request, headers);
    headers.set("Location", "/home");

    return new Response(null, {
      status: 302,
      headers,
    });
  }),
];
