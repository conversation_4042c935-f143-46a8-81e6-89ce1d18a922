"use client";

import { useState } from "react";
import { But<PERSON> } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";
import { Textarea } from "@/app/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select";
import { Checkbox } from "@/app/components/ui/checkbox";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card";
import { updateProfile } from "@/app/pages/user/profile/functions";

interface ProfileData {
  id: string;
  name?: string;
  bio?: string;
  location?: string;
  experienceLevel?: string;
  sailingExperience?: string;
  certifications?: string[];
  boatInformation?: {
    boatType?: string;
    boatName?: string;
    sailNumber?: string;
    yearBuilt?: string;
    manufacturer?: string;
  };
  clubAffiliation?: string;
  privacySettings?: {
    showEmail?: boolean;
    showLocation?: boolean;
    showExperience?: boolean;
    showBoatInfo?: boolean;
    showActivity?: boolean;
  };
  profilePicture?: string;
}

interface ProfileEditFormProps {
  profile: ProfileData;
  userId: string;
  username: string;
}

export function ProfileEditForm({ profile, userId, username }: ProfileEditFormProps) {
  const [formData, setFormData] = useState<ProfileData>({
    id: profile.id,
    name: profile.name || "",
    bio: profile.bio || "",
    location: profile.location || "",
    experienceLevel: profile.experienceLevel || "",
    sailingExperience: profile.sailingExperience || "",
    certifications: profile.certifications || [],
    boatInformation: profile.boatInformation || {
      boatType: "",
      boatName: "",
      sailNumber: "",
      yearBuilt: "",
      manufacturer: "",
    },
    clubAffiliation: profile.clubAffiliation || "",
    privacySettings: profile.privacySettings || {
      showEmail: false,
      showLocation: true,
      showExperience: true,
      showBoatInfo: true,
      showActivity: true,
    },
    profilePicture: profile.profilePicture || "",
  });

  const [certificationInput, setCertificationInput] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleBoatInfoChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      boatInformation: {
        ...prev.boatInformation,
        [field]: value
      }
    }));
  };

  const handlePrivacyChange = (field: string, value: boolean) => {
    setFormData(prev => ({
      ...prev,
      privacySettings: {
        ...prev.privacySettings,
        [field]: value
      }
    }));
  };

  const addCertification = () => {
    if (certificationInput.trim()) {
      setFormData(prev => ({
        ...prev,
        certifications: [...(prev.certifications || []), certificationInput.trim()]
      }));
      setCertificationInput("");
    }
  };

  const removeCertification = (index: number) => {
    setFormData(prev => ({
      ...prev,
      certifications: prev.certifications?.filter((_, i) => i !== index) || []
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const result = await updateProfile(userId, formData);

      if (result.success) {
        alert("Profile updated successfully!");
        // Navigate back to profile view
        window.location.href = `/user/${username}/profile`;
      } else {
        alert(result.error || "Failed to update profile");
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      alert("An unexpected error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
          <CardDescription>Update your basic profile information</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="name">Full Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              placeholder="Enter your full name"
            />
          </div>

          <div>
            <Label htmlFor="bio">Bio</Label>
            <Textarea
              id="bio"
              value={formData.bio}
              onChange={(e) => handleInputChange("bio", e.target.value)}
              placeholder="Tell us about yourself"
              rows={3}
            />
          </div>

          <div>
            <Label htmlFor="location">Location</Label>
            <Input
              id="location"
              value={formData.location}
              onChange={(e) => handleInputChange("location", e.target.value)}
              placeholder="City, Country"
            />
          </div>

          <div>
            <Label htmlFor="clubAffiliation">Club Affiliation</Label>
            <Input
              id="clubAffiliation"
              value={formData.clubAffiliation}
              onChange={(e) => handleInputChange("clubAffiliation", e.target.value)}
              placeholder="Your sailing club"
            />
          </div>
        </CardContent>
      </Card>

      {/* Sailing Experience */}
      <Card>
        <CardHeader>
          <CardTitle>Sailing Experience</CardTitle>
          <CardDescription>Share your sailing background</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="experienceLevel">Experience Level</Label>
            <Select
              value={formData.experienceLevel}
              onValueChange={(value) => handleInputChange("experienceLevel", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select your experience level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="beginner">Beginner</SelectItem>
                <SelectItem value="intermediate">Intermediate</SelectItem>
                <SelectItem value="advanced">Advanced</SelectItem>
                <SelectItem value="expert">Expert</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="sailingExperience">Sailing Experience Details</Label>
            <Textarea
              id="sailingExperience"
              value={formData.sailingExperience}
              onChange={(e) => handleInputChange("sailingExperience", e.target.value)}
              placeholder="Describe your sailing experience"
              rows={3}
            />
          </div>

          {/* Certifications */}
          <div>
            <Label>Certifications</Label>
            <div className="space-y-2">
              {formData.certifications?.map((cert, index) => (
                <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                  <span>{cert}</span>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeCertification(index)}
                  >
                    Remove
                  </Button>
                </div>
              ))}
              <div className="flex space-x-2">
                <Input
                  value={certificationInput}
                  onChange={(e) => setCertificationInput(e.target.value)}
                  placeholder="Add a certification"
                  onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addCertification())}
                />
                <Button type="button" onClick={addCertification}>
                  Add
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Boat Information */}
      <Card>
        <CardHeader>
          <CardTitle>Boat Information</CardTitle>
          <CardDescription>Details about your boat</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="boatType">Boat Type</Label>
              <Input
                id="boatType"
                value={formData.boatInformation?.boatType || ""}
                onChange={(e) => handleBoatInfoChange("boatType", e.target.value)}
                placeholder="e.g., ILCA 6"
              />
            </div>
            <div>
              <Label htmlFor="boatName">Boat Name</Label>
              <Input
                id="boatName"
                value={formData.boatInformation?.boatName || ""}
                onChange={(e) => handleBoatInfoChange("boatName", e.target.value)}
                placeholder="Your boat's name"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="sailNumber">Sail Number</Label>
              <Input
                id="sailNumber"
                value={formData.boatInformation?.sailNumber || ""}
                onChange={(e) => handleBoatInfoChange("sailNumber", e.target.value)}
                placeholder="e.g., NOR 218401"
              />
            </div>
            <div>
              <Label htmlFor="yearBuilt">Year Built</Label>
              <Input
                id="yearBuilt"
                value={formData.boatInformation?.yearBuilt || ""}
                onChange={(e) => handleBoatInfoChange("yearBuilt", e.target.value)}
                placeholder="e.g., 2020"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="manufacturer">Manufacturer</Label>
            <Input
              id="manufacturer"
              value={formData.boatInformation?.manufacturer || ""}
              onChange={(e) => handleBoatInfoChange("manufacturer", e.target.value)}
              placeholder="e.g., PSA"
            />
          </div>
        </CardContent>
      </Card>

      {/* Privacy Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Privacy Settings</CardTitle>
          <CardDescription>Control what information is visible to others</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="showEmail"
              checked={formData.privacySettings?.showEmail || false}
              onCheckedChange={(checked) => handlePrivacyChange("showEmail", !!checked)}
            />
            <Label htmlFor="showEmail">Show email address</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="showLocation"
              checked={formData.privacySettings?.showLocation || false}
              onCheckedChange={(checked) => handlePrivacyChange("showLocation", !!checked)}
            />
            <Label htmlFor="showLocation">Show location</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="showExperience"
              checked={formData.privacySettings?.showExperience || false}
              onCheckedChange={(checked) => handlePrivacyChange("showExperience", !!checked)}
            />
            <Label htmlFor="showExperience">Show sailing experience</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="showBoatInfo"
              checked={formData.privacySettings?.showBoatInfo || false}
              onCheckedChange={(checked) => handlePrivacyChange("showBoatInfo", !!checked)}
            />
            <Label htmlFor="showBoatInfo">Show boat information</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="showActivity"
              checked={formData.privacySettings?.showActivity || false}
              onCheckedChange={(checked) => handlePrivacyChange("showActivity", !!checked)}
            />
            <Label htmlFor="showActivity">Show activity status</Label>
          </div>
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="flex justify-end space-x-4">
        <Button type="button" variant="outline" onClick={() => window.history.back()}>
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "Saving..." : "Save Changes"}
        </Button>
      </div>
    </form>
  );
}
